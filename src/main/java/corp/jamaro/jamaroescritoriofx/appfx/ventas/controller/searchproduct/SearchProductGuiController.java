package corp.jamaro.jamaroescritoriofx.appfx.ventas.controller.searchproduct;

import corp.jamaro.jamaroescritoriofx.appfx.controller.BaseController;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.Item;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.Producto;
import corp.jamaro.jamaroescritoriofx.appfx.service.SpringFXMLLoader;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.controller.SaleController;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.controller.SaleGuiController;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.gui.SearchProductGui;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.service.gui.SearchProductGuiService;
import javafx.application.Platform;
import javafx.beans.binding.Bindings;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.fxml.FXML;
import javafx.scene.Parent;
import javafx.scene.control.ListCell;
import javafx.scene.control.ListView;
import javafx.scene.control.ProgressIndicator;
import javafx.scene.layout.AnchorPane;
import javafx.scene.layout.Region;
import javafx.scene.layout.StackPane;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.net.URL;
import java.util.List;
import java.util.Objects;
import java.util.ResourceBundle;
import java.util.UUID;

/**
 * Controlador para la vista de búsqueda de productos.
 * Incorpora carga de productos, bindings reactivos y manejo reactivo de suscripciones.
 * Utiliza un ListView con celdas personalizadas para mostrar los productos.
 */
@Component
@Scope("prototype")
@RequiredArgsConstructor
@Slf4j
public class SearchProductGuiController extends BaseController {

    // ===== Modelo y servicios inyectados =====
    @Setter
    private SearchProductGui searchProductGui;  // Modelo de búsqueda
    private final SearchProductGuiService searchProductGuiService;
    private final SpringFXMLLoader springFXMLLoader;

    // ===== Controles definidos en FXML =====
    @FXML private ListView<Producto> productsListView;     // ListView para mostrar productos
    @FXML private AnchorPane anchorFilters;                // Panel donde se carga la vista de filtros
    @FXML private ProgressIndicator loadingIndicator;      // Indicador de carga
    @FXML private StackPane stackPaneSearchProduct;        // Panel principal

    // ===== Controlador de filtros (cargado desde FXML) =====
    private SearchFilterController filterController;

    // ===== Suscripción a actualizaciones vía RSocket =====
    private reactor.core.Disposable searchSubscription;
    // Guarda el modelo anterior para evitar actualizaciones innecesarias
    private SearchProductGui lastLoadedGui;

    // ===== Propiedad para indicar si se está realizando una búsqueda activa =====
    private final BooleanProperty searchActive = new SimpleBooleanProperty(true);

    // ===== Referencia al controlador del producto actualmente expandido =====
    private ProductoItemSearchedController currentExpandedController;

    // Interfaces funcionales para manejar eventos de clic
    private ProductClickHandler productClickHandler;
    private ProductDoubleClickHandler productDoubleClickHandler;
    private ItemClickHandler itemClickHandler;
    private ItemDoubleClickHandler itemDoubleClickHandler;

    // Interfaces funcionales para los diferentes tipos de eventos
    @FunctionalInterface
    public interface ProductClickHandler {
        void handle(Producto producto);
    }

    @FunctionalInterface
    public interface ProductDoubleClickHandler {
        void handle(Producto producto);
    }

    @FunctionalInterface
    public interface ItemClickHandler {
        void handle(Item item, Producto producto);
    }

    @FunctionalInterface
    public interface ItemDoubleClickHandler {
        void handle(Item item, Producto producto);
    }

    // Referencia al SaleController para agregar items a la venta
    private SaleController saleController;

    /**
     * Establece el manejador para eventos de clic en productos
     * @param handler Función que recibe el producto seleccionado
     */
    public void setProductClickHandler(ProductClickHandler handler) {
        this.productClickHandler = handler;
    }

    /**
     * Establece el manejador para eventos de doble clic en productos
     * @param handler Función que recibe el producto seleccionado
     */
    public void setProductDoubleClickHandler(ProductDoubleClickHandler handler) {
        this.productDoubleClickHandler = handler;
    }

    /**
     * Establece el manejador para eventos de clic en items
     * @param handler Función que recibe el item y su producto padre
     */
    public void setItemClickHandler(ItemClickHandler handler) {
        this.itemClickHandler = handler;
    }

    /**
     * Establece el manejador para eventos de doble clic en items
     * @param handler Función que recibe el item y su producto padre
     */
    public void setItemDoubleClickHandler(ItemDoubleClickHandler handler) {
        this.itemDoubleClickHandler = handler;
    }

    /**
     * Establece el SaleController para agregar items a la venta
     * @param saleController El controlador de la venta
     */
    public void setSaleController(SaleController saleController) {
        this.saleController = saleController;

        // Configurar el manejador de doble clic en items para agregar a la venta
        if (saleController != null) {
            setItemDoubleClickHandler((item, producto) -> {
                log.debug("Agregando item {} a la venta", item.getCodCompuesto());
                saleController.addItemToSale(item, producto);
            });
        }
    }

    /**
     * Se suscribe a los cambios del SearchProductGui para recargar los productos
     * solo cuando el modelo cambia realmente.
     * Optimizado para minimizar actualizaciones innecesarias y mejorar el rendimiento.
     */
    public void setSearchProductGuiId(UUID spId) {
        log.info("Suscribiéndose a cambios de SearchProductGui con id={}", spId);
        searchSubscription = searchProductGuiService.subscribeToChanges(spId)
                .subscribe(
                        updatedGui -> handleSearchProductGuiUpdate(updatedGui),
                        error -> log.error("Error al suscribirse a cambios de SearchProductGui (id {}): {}",
                                spId, error.getMessage(), error)
                );
        registerSubscription(searchSubscription);
    }

    /**
     * Maneja las actualizaciones del SearchProductGui de manera eficiente.
     * Solo actualiza la UI y recarga productos cuando hay cambios relevantes.
     */
    private void handleSearchProductGuiUpdate(SearchProductGui updatedGui) {
        // Registrar la actualización recibida (nivel debug para no saturar logs)
        log.debug("Recibida actualización de SearchProductGui: id={}, descripcion='{}', codProductoOld='{}', codFabricaOld='{}', vehiculoSearch='{}'",
                updatedGui.getId(),
                updatedGui.getDescripcion(),
                updatedGui.getCodProductoOld(),
                updatedGui.getCodFabricaOld(),
                updatedGui.getVehiculoSearch());

        // Si es la primera actualización, siempre actualizar
        if (lastLoadedGui == null) {
            log.info("Primera actualización de SearchProductGui, cargando datos iniciales");
            updateSearchProductGuiAndLoadProducts(updatedGui);
            return;
        }

        // Verificar si hay cambios relevantes que requieran actualizar la UI o recargar productos
        if (hasRelevantChanges(lastLoadedGui, updatedGui)) {
            updateSearchProductGuiAndLoadProducts(updatedGui);
        } else {
            log.debug("No se detectaron cambios relevantes en SearchProductGui, ignorando actualización");
        }
    }

    /**
     * Verifica si hay cambios relevantes entre dos instancias de SearchProductGui.
     * @return true si hay cambios que requieren actualizar la UI o recargar productos
     */
    private boolean hasRelevantChanges(SearchProductGui oldGui, SearchProductGui newGui) {
        // Comparar campos individualmente en lugar de usar equals
        boolean idChanged = !Objects.equals(oldGui.getId(), newGui.getId());
        boolean idGrupoChanged = !Objects.equals(oldGui.getIdGrupo(), newGui.getIdGrupo());
        boolean descripcionChanged = !Objects.equals(oldGui.getDescripcion(), newGui.getDescripcion());
        boolean codProductoOldChanged = !Objects.equals(oldGui.getCodProductoOld(), newGui.getCodProductoOld());
        boolean codFabricaOldChanged = !Objects.equals(oldGui.getCodFabricaOld(), newGui.getCodFabricaOld());
        boolean vehiculoSearchChanged = !Objects.equals(oldGui.getVehiculoSearch(), newGui.getVehiculoSearch());
        boolean filtroDatoRellenadosChanged = !Objects.equals(oldGui.getFiltroDatoRellenados(), newGui.getFiltroDatoRellenados());
        boolean createdAtChanged = !Objects.equals(oldGui.getCreatedAt(), newGui.getCreatedAt());

        boolean hasChanges = idChanged || idGrupoChanged || descripcionChanged ||
                          codProductoOldChanged || codFabricaOldChanged ||
                          vehiculoSearchChanged || filtroDatoRellenadosChanged ||
                          createdAtChanged;

        if (hasChanges) {
            // Registrar los cambios detectados (solo si hay cambios, para reducir logs)
            log.info("Detectado cambio en SearchProductGui - Actualizando:");
            if (idChanged) log.info(" - id: {} -> {}", oldGui.getId(), newGui.getId());
            if (idGrupoChanged) log.info(" - idGrupo: {} -> {}", oldGui.getIdGrupo(), newGui.getIdGrupo());
            if (descripcionChanged) log.info(" - descripcion: {} -> {}", oldGui.getDescripcion(), newGui.getDescripcion());
            if (codProductoOldChanged) log.info(" - codProductoOld: {} -> {}", oldGui.getCodProductoOld(), newGui.getCodProductoOld());
            if (codFabricaOldChanged) log.info(" - codFabricaOld: {} -> {}", oldGui.getCodFabricaOld(), newGui.getCodFabricaOld());
            if (vehiculoSearchChanged) log.info(" - vehiculoSearch: {} -> {}", oldGui.getVehiculoSearch(), newGui.getVehiculoSearch());
            if (filtroDatoRellenadosChanged) log.info(" - filtroDatoRellenados cambiados");
            if (createdAtChanged) log.info(" - createdAt: {} -> {}", oldGui.getCreatedAt(), newGui.getCreatedAt());
        }

        return hasChanges;
    }

    /**
     * Actualiza el modelo SearchProductGui y carga los productos desde el servidor.
     */
    private void updateSearchProductGuiAndLoadProducts(SearchProductGui updatedGui) {
        log.info("Actualizando SearchProductGui y cargando productos");
        lastLoadedGui = updatedGui;
        this.searchProductGui = updatedGui;

        // Actualizar el controlador de filtros si existe
        runOnUiThread(() -> {
            if (filterController != null) {
                filterController.setSearchProductGui(updatedGui);
                filterController.enableControls(true);
            }
        });

        // Cargar productos desde el servidor
        loadProducts();
    }

    @Override
    public void initialize(URL url, ResourceBundle resourceBundle) {
        try {
            // Carga la vista de filtros desde FXML y la configura para ocupar todo el panel
            Parent filterView = springFXMLLoader.load("fxml/sale/searchproduct/searchFilter.fxml");
            AnchorPane.setTopAnchor(filterView, 0.0);
            AnchorPane.setRightAnchor(filterView, 0.0);
            AnchorPane.setBottomAnchor(filterView, 0.0);
            AnchorPane.setLeftAnchor(filterView, 0.0);
            filterController = springFXMLLoader.getController(filterView);
            // Establecer la referencia a este controlador en el filterController
            filterController.setParentController(this);

            if (searchProductGui != null) {
                filterController.setSearchProductGui(searchProductGui);
                filterController.enableControls(true);
            } else {
                filterController.enableControls(false);
            }
            anchorFilters.getChildren().setAll(filterView);

            // Configurar el cell factory para el ListView de productos
            configurarProductListView();

            // Binding reactivo para el indicador de carga:
            // Se muestra cuando la lista está vacía y la búsqueda está activa.
            loadingIndicator.visibleProperty().bind(
                    Bindings.createBooleanBinding(
                            () -> productsListView.getItems().isEmpty() && searchActive.get(),
                            productsListView.itemsProperty(), searchActive
                    )
            );
        } catch (Exception e) {
            log.error("Error al cargar searchFilter.fxml: {}", e.getMessage(), e);
        }
    }

    /**
     * Configura el ListView de productos con un cell factory personalizado
     * que utiliza el componente ProductoItemSearched para mostrar cada producto.
     */
    private void configurarProductListView() {
        // Desactivar el comportamiento de scroll automático al seleccionar elementos
        productsListView.setOnScrollTo(event -> event.consume());

        // Configurar el comportamiento de selección para evitar el scroll automático
        productsListView.getSelectionModel().selectedItemProperty().addListener((obs, oldVal, newVal) -> {
            // No hacer nada aquí, solo interceptar el evento para evitar el scroll automático
        });

        // Configurar el comportamiento de foco para el ListView
        productsListView.setOnMouseEntered(e -> {
            if (!productsListView.isFocused()) {
                productsListView.requestFocus();
            }
        });

        // Permitir que las celdas ajusten su altura automáticamente
        productsListView.setFixedCellSize(-1);

        // Agregar detector de doble clic para el ListView
        productsListView.setOnMouseClicked(event -> {
            // Verificar si el clic ocurrió en una celda vacía
            int clickedIndex = productsListView.getSelectionModel().getSelectedIndex();
            if (clickedIndex >= productsListView.getItems().size() || clickedIndex < 0) {
                // Clic en celda vacía, ignorar
                log.debug("Clic detectado en celda vacía del ListView, ignorando");
                event.consume();
                return;
            }

            Producto selectedProducto = productsListView.getSelectionModel().getSelectedItem();
            if (selectedProducto != null) {
                if (event.getClickCount() == 2 && productDoubleClickHandler != null) {
                    // Doble clic en producto
                    log.debug("Doble clic en producto: {}", selectedProducto.getCodProductoOld());
                    productDoubleClickHandler.handle(selectedProducto);
                    event.consume();
                } else if (event.getClickCount() == 1 && productClickHandler != null) {
                    // Clic simple en producto (solo si no hay un controlador expandido actualmente)
                    // para evitar conflictos con el clic de expansión
                    if (currentExpandedController == null) {
                        log.debug("Clic simple en producto: {}", selectedProducto.getCodProductoOld());
                        productClickHandler.handle(selectedProducto);
                        event.consume();
                    }
                }
            }
        });

        // Configurar el ListView para que no muestre celdas vacías
        productsListView.setFixedCellSize(Region.USE_COMPUTED_SIZE);

        productsListView.setCellFactory(lv -> new ListCell<Producto>() {
            private Parent productoView;
            private ProductoItemSearchedController controller;

            @Override
            protected void updateItem(Producto producto, boolean empty) {
                super.updateItem(producto, empty);

                if (empty || producto == null) {
                    setGraphic(null);
                    // Deshabilitar eventos de mouse en celdas vacías
                    setDisable(true);
                    setMouseTransparent(true);
                } else {
                    // Habilitar eventos de mouse en celdas con contenido
                    setDisable(false);
                    setMouseTransparent(false);
                    try {
                        // Crear una nueva vista para cada producto
                        // Esto es importante para evitar problemas con la reutilización de celdas
                        productoView = springFXMLLoader.load("fxml/sale/searchproduct/productoItemSearched.fxml");
                        controller = springFXMLLoader.getController(productoView);
                        // Pasar el SearchProductGui al controlador para que pueda ordenar los atributos
                        if (searchProductGui != null) {
                            controller.setSearchProductGui(searchProductGui);
                        }
                        controller.setProducto(producto);

                        // Propagar los handlers de eventos a los controladores de los productos
                        if (itemClickHandler != null) {
                            controller.setItemClickHandler(itemClickHandler);
                        }
                        if (itemDoubleClickHandler != null) {
                            controller.setItemDoubleClickHandler(itemDoubleClickHandler);
                        }

                        // Configurar el ancho del componente para que ocupe todo el ancho disponible
                        // Restar 15 píxeles para dar espacio a la barra de desplazamiento vertical
                        productoView.prefWidth(lv.getWidth() - 15);

                        // Asegurar que el componente se ajuste al ancho del ListView
                        AnchorPane rootPane = (AnchorPane) productoView;
                        rootPane.prefWidthProperty().bind(lv.widthProperty().subtract(15));

                        // Configurar el evento de clic en la celda para expandir/contraer
                        // Ya no necesitamos configurar el evento aquí porque lo hemos configurado directamente en el root del producto
                        // Pero mantenemos la capacidad de hacer clic en la celda para mayor flexibilidad
                        this.setOnMouseClicked(event -> {
                            // Propagar el evento al controlador del producto en el hilo de UI
                            final Producto currentProducto = producto; // Hacer efectivamente final para lambda
                            runOnUiThread(() -> {
                                log.debug("Clic en celda de ListView para el producto {}",
                                         currentProducto != null ? currentProducto.getCodProductoOld() : "null");

                                // Determinar si este producto ya está expandido
                                boolean isCurrentlyExpanded = controller.isExpanded();

                                // Si estamos haciendo clic en un producto diferente al que está expandido actualmente
                                if (currentExpandedController != null && currentExpandedController != controller) {
                                    // Colapsar el producto actualmente expandido
                                    log.debug("Colapsando producto previamente expandido: {}",
                                             currentExpandedController.getProductoId());
                                    currentExpandedController.setExpanded(false);
                                    currentExpandedController = null;
                                }

                                // Si el producto clickeado no estaba expandido, expandirlo
                                if (!isCurrentlyExpanded) {
                                    log.debug("Expandiendo producto: {}", currentProducto.getCodProductoOld());

                                    // Expandir el producto sin hacer scroll
                                    controller.setExpanded(true);
                                    currentExpandedController = controller;
                                } else {
                                    // Si el producto clickeado ya estaba expandido, colapsarlo
                                    log.debug("Colapsando producto: {}", currentProducto.getCodProductoOld());
                                    controller.setExpanded(false);
                                    currentExpandedController = null;
                                }
                            });
                        });

                        // Asegurar que el cursor sea de mano para indicar que es clickeable
                        this.setCursor(javafx.scene.Cursor.HAND);

                        setGraphic(productoView);
                    } catch (Exception e) {
                        log.error("Error al cargar la vista de producto: {}", e.getMessage(), e);
                        setGraphic(null);
                    }
                }
            }
        });

        // No es necesario configurar el ancho aquí, ya se hace en el FXML
    }

    /**
     * Carga reactiva de productos desde el servidor.
     * Optimizado para proporcionar más información de depuración y manejo de errores.
     */
    private void loadProducts() {
        if (searchProductGui == null) {
            log.warn("loadProducts(): SearchProductGui es nulo, no se pueden cargar productos.");
            return;
        }

        // Registrar los valores de búsqueda actuales
        log.info("Iniciando búsqueda de productos con: descripcion='{}', codProductoOld='{}', codFabricaOld='{}', vehiculoSearch='{}'",
                searchProductGui.getDescripcion(),
                searchProductGui.getCodProductoOld(),
                searchProductGui.getCodFabricaOld(),
                searchProductGui.getVehiculoSearch());

        // Activa la bandera de búsqueda activa para mostrar el indicador de carga
        searchActive.set(true);

        // Guardar el ID actual para verificar que la respuesta corresponda a la última solicitud
        final UUID requestId = searchProductGui.getId();

        subscribeOnBoundedElastic(
                searchProductGuiService.getProductsSearched(searchProductGui)
                        .collectList(),
                productos -> {
                    // Verificar que la respuesta corresponda a la última solicitud
                    if (searchProductGui == null || !Objects.equals(searchProductGui.getId(), requestId)) {
                        log.warn("Recibida respuesta para una solicitud obsoleta (id={}), ignorando", requestId);
                        return;
                    }

                    searchActive.set(false);
                    log.info("Productos obtenidos: {} para SearchProductGui con id={}, codProductoOld='{}'",
                            productos.size(), searchProductGui.getId(), searchProductGui.getCodProductoOld());

                    // Actualizar la lista de productos en el hilo de UI
                    updateProductList(productos);
                },
                error -> {
                    searchActive.set(false);
                    log.error("Error al cargar productos: {}", error.getMessage(), error);
                    runOnUiThread(() -> {
                        // Limpiar la lista de productos en caso de error para evitar mostrar datos obsoletos
                        productsListView.getItems().clear();
                    });
                }
        );
    }

    // ===== Actualiza el ListView de productos =====
    private void updateProductList(List<Producto> productos) {
        runOnUiThread(() -> {
            // Limpiar la lista actual y el controlador expandido
            productsListView.getItems().clear();
            currentExpandedController = null;

            // Agregar los nuevos productos
            productsListView.getItems().addAll(productos);

            // Ajustar la altura del ListView para mostrar solo las celdas con contenido
            Platform.runLater(() -> {
                // Forzar un layout para asegurar que las celdas se actualicen correctamente
                productsListView.requestLayout();

                log.debug("ListView actualizado con {} productos", productos.size());
            });
        });
    }

    /**
     * Método público para forzar una recarga de productos.
     * Puede ser llamado desde otros controladores cuando se actualicen los comodines.
     */
    public void forceProductReload() {
        log.info("Forzando recarga de productos");
        loadProducts();
    }

    // ===== Limpieza de recursos al cerrar la vista =====
    @Override
    public void onClose() {
        super.onClose();
        if (searchSubscription != null && !searchSubscription.isDisposed()) {
            searchSubscription.dispose();
        }
        log.info("SearchProductGuiController cerrado.");
    }
}
